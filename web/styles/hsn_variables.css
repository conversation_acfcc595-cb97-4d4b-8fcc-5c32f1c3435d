/* =======================================
 * 遊牧好點 + HSN 融合變數系統
 * ======================================= */

:root {
  /* === HSN 核心變數 (直接採用) === */

  /* 霓虹顏色系統 */
  --color-neon-green: #00ff41;      /* HSN 螢光綠 */
  --color-neon-cyan: #00ffff;       /* HSN 螢光青 */
  --color-neon-magenta: #ff00ff;    /* HSN 螢光紫 */
  --color-neon-yellow: #ffff00;     /* HSN 螢光黃 */

  /* 琥珀色系統 */
  --color-amber-primary: #ffb000;   /* HSN 主琥珀 */
  --color-amber-secondary: #ff8000; /* HSN 次琥珀 */
  --color-amber-tertiary: #e6a366;  /* HSN 三級琥珀 */

  /* HSN 間距系統 */
  --spacing-xs: 4px;    /* 微小間距 */
  --spacing-sm: 8px;    /* 小間距 */
  --spacing-md: 12px;   /* 中等間距 */
  --spacing-lg: 20px;   /* 大間距 */
  --spacing-xl: 30px;   /* 超大間距 */

  /* HSN 字體系統 */
  --font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 18px;

  /* HSN 動畫時間 */
  --anim-fast: 0.3s;      /* 快速互動反饋 */
  --anim-normal: 1s;      /* 標準過渡效果 */
  --anim-slow: 3s;        /* 緩慢裝飾動畫 */
  --anim-very-slow: 8s;   /* 背景循環動畫 */

  /* HSN 陰影效果 */
  --shadow-glow-amber: 0 0 15px var(--color-amber-primary);
  --shadow-glow-strong: 0 0 25px rgba(255, 176, 0, 0.5), 0 0 35px rgba(255, 128, 0, 0.3);
  --shadow-glow-neon: 0 0 20px var(--color-neon-green);

  /* === 遊牧好點專屬適配 === */

  /* 主要色彩系統 (保留 Jaspr 基礎) */
  --color-primary: #01589B;                    /* 遊牧藍 (專業感) */
  --color-primary-light: #3b82f6;              /* 淺藍 */
  --color-primary-dark: #014a7a;               /* 深藍 */

  /* 功能色彩映射 */
  --color-workspace: var(--color-amber-primary);     /* 工作空間 → 琥珀 */
  --color-interaction: var(--color-neon-green);      /* 互動強調 → 螢光綠 */
  --color-tech: var(--color-neon-cyan);              /* 科技感 → 螢光青 */
  --color-energy: var(--color-neon-yellow);          /* 活力感 → 螢光黃 */
  --color-premium: var(--color-neon-magenta);        /* 高級感 → 螢光紫 */

  /* 遊牧好點語意色彩 */
  --color-nomad-explore: #ff6b35;             /* 探索橘 */
  --color-nomad-work: #8b5cf6;                /* 工作紫 */
  --color-nomad-community: #10b981;           /* 社群綠 */
  --color-nomad-travel: #f59e0b;              /* 旅行金 */

  /* 功能狀態色彩 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* 灰階系統 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 背景系統 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-muted: #f1f5f9;
  --bg-dark: linear-gradient(135deg, #0f0f0f 0%, #1f1f1f 100%);

  /* 邊框和圓角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  /* Z-index 層級 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* === 暗色主題變數 === */
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-muted: #374151;
  --color-gray-50: #f9fafb;
  --color-gray-900: #111827;
}

/* === HSN 霓虹主題變數 === */
[data-theme="neon"] {
  --color-primary: var(--color-neon-cyan);
  --bg-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  --bg-secondary: rgba(0, 0, 0, 0.95);
}