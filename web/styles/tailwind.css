/* 匯入 HSN 設計系統 */
@import './hsn_variables.css';
@import './hsn_components.css';
@import './hsn_animations.css';

/* Custom Tailwind CSS styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component classes */
@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200;
  }
  
  .card {
    @apply bg-white shadow-lg rounded-lg p-6 border border-gray-200;
  }
  
  .nav-link {
    @apply text-gray-600 hover:text-blue-600 transition-colors duration-200;
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* === 新增：HSN + Tailwind 橋接樣式 === */

/* 將 HSN 變數映射到 Tailwind 工具類 */
.bg-nomad-primary { background-color: var(--color-primary); }
.bg-nomad-workspace { background-color: var(--color-workspace); }
.bg-nomad-tech { background-color: var(--color-tech); }
.bg-nomad-interaction { background-color: var(--color-interaction); }

.text-nomad-primary { color: var(--color-primary); }
.text-nomad-workspace { color: var(--color-workspace); }
.text-nomad-tech { color: var(--color-tech); }
.text-nomad-interaction { color: var(--color-interaction); }

.border-nomad-primary { border-color: var(--color-primary); }
.border-nomad-interaction { border-color: var(--color-interaction); }

/* HSN 間距映射 */
.p-nomad-xs { padding: var(--spacing-xs); }
.p-nomad-sm { padding: var(--spacing-sm); }
.p-nomad-md { padding: var(--spacing-md); }
.p-nomad-lg { padding: var(--spacing-lg); }
.p-nomad-xl { padding: var(--spacing-xl); }

.m-nomad-xs { margin: var(--spacing-xs); }
.m-nomad-sm { margin: var(--spacing-sm); }
.m-nomad-md { margin: var(--spacing-md); }
.m-nomad-lg { margin: var(--spacing-lg); }
.m-nomad-xl { margin: var(--spacing-xl); }

/* HSN 動畫持續時間 */
.duration-nomad-fast { transition-duration: var(--anim-fast); }
.duration-nomad-normal { transition-duration: var(--anim-normal); }
.duration-nomad-slow { transition-duration: var(--anim-slow); }

/* 組合工具類 */
.nomad-card {
  @apply bg-white rounded-lg shadow-md p-nomad-lg duration-nomad-fast hover:shadow-xl hover:-translate-y-1;
}

.nomad-input {
  @apply w-full p-nomad-md border border-gray-300 rounded-md duration-nomad-fast;
  @apply focus:outline-none focus:border-nomad-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20;
}

.nomad-button {
  @apply px-nomad-lg py-nomad-md rounded-md font-semibold duration-nomad-fast;
  @apply hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2;
}
