import 'package:jaspr/jaspr.dart';

import '../components/counter.dart';
import '../components/heroicon.dart';

// By using the @client annotation this component will be automatically compiled to javascript and mounted
// on the client. Therefore:
// - this file and any imported file must be compilable for both server and client environments.
// - this component and any child components will be built once on the server during pre-rendering and then
//   again on the client during normal rendering.
@client
class Home extends StatefulComponent {
  const Home({super.key});

  @override
  State<Home> createState() => HomeState();
}

class HomeState extends State<Home> {
  @override
  void initState() {
    super.initState();
    // Run code depending on the rendering environment.
    if (kIsWeb) {
      print("Hello client");
      // When using @client components there is no default `main()` function on the client where you would normally
      // run any client-side initialization logic. Instead you can put it here, considering this component is only
      // mounted once at the root of your client-side component tree.
    } else {
      print("Hello server");
    }
  }

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield section(
        classes:
            'min-h-screen bg-gray-50 flex flex-col items-center justify-center p-8',
        [
          div(classes: 'card max-w-md w-full text-center', [
            img(src: 'images/logo.svg', width: 80, classes: 'mx-auto mb-6'),
            h1(
                classes: 'text-4xl font-bold text-gray-800 mb-4 text-shadow',
                [text('遊牧好點')]),
            p(classes: 'text-lg text-gray-600 mb-8', [
              text(
                  '探索台灣最佳數位遊牧工作空間，享受 HSN 科技感設計體驗！')
            ]),
            // Feature icons
            div(classes: 'flex justify-center space-x-8 mb-8', [
              div(classes: 'flex flex-col items-center', [
                const Heroicon.solid(HeroiconName.star, classes: 'w-8 h-8 text-yellow-500 mb-2'),
                span(classes: 'text-sm text-gray-600', [text('Fast')]),
              ]),
              div(classes: 'flex flex-col items-center', [
                const Heroicon.solid(HeroiconName.heart, classes: 'w-8 h-8 text-red-500 mb-2'),
                span(classes: 'text-sm text-gray-600', [text('Beautiful')]),
              ]),
              div(classes: 'flex flex-col items-center', [
                const Heroicon.solid(HeroiconName.checkCircle, classes: 'w-8 h-8 text-green-500 mb-2'),
                span(classes: 'text-sm text-gray-600', [text('Reliable')]),
              ]),
            ]),
            div(classes: 'space-y-4', [
              const Counter(),
              // 測試新的 HSN 整合樣式
              div(classes: 'flex flex-wrap gap-4 justify-center', [
                // 使用新的 HSN 按鈕樣式
                button(classes: 'btn-nomad-primary flex items-center gap-2', [
                  const Heroicon.solid(HeroiconName.arrowTurnRightDown, classes: 'w-5 h-5'),
                  text('開始探索工作空間'),
                ]),
                button(classes: 'btn-nomad-tech flex items-center gap-2', [
                  const Heroicon.outline(HeroiconName.informationCircle, classes: 'w-5 h-5'),
                  text('AI 智能推薦'),
                ]),
                button(classes: 'btn-nomad-workspace flex items-center gap-2', [
                  const Heroicon.outline(HeroiconName.paintBrush, classes: 'w-5 h-5'),
                  text('探索特色空間'),
                ]),
              ]),
            ]),
          ]),

          // 測試工作空間卡片
          div(classes: 'mt-12 max-w-6xl w-full', [
            h2(classes: 'text-2xl font-bold text-center mb-8 text-gray-800', [
              text('精選工作空間')
            ]),
            div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6', [
              // 測試卡片 1
              div(classes: 'workspace-card-nomad', [
                div(classes: 'flex items-center mb-3', [
                  const Heroicon.solid(HeroiconName.flag, classes: 'w-5 h-5 text-nomad-primary mr-2'),
                  span(classes: 'font-semibold text-gray-800', [text('台北大安區')])
                ]),
                h3(classes: 'text-lg font-bold mb-2', [text('星巴克 敦南門市')]),
                p(classes: 'text-gray-600 mb-4', [text('寬敞舒適的工作環境，提供高速 Wi-Fi 和充足插座')]),
                div(classes: 'flex items-center space-x-4 text-sm', [
                  div(classes: 'wifi-indicator wifi-excellent', [text('Wi-Fi 優秀')]),
                  div(classes: 'flex items-center', [
                    const Heroicon.outline(HeroiconName.heart, classes: 'w-4 h-4 mr-1'),
                    text('咖啡供應')
                  ])
                ])
              ]),

              // 測試卡片 2 - 特色推薦
              div(classes: 'workspace-card-nomad workspace-card-featured', [
                div(classes: 'flex items-center mb-3', [
                  const Heroicon.solid(HeroiconName.flag, classes: 'w-5 h-5 text-nomad-tech mr-2'),
                  span(classes: 'font-semibold text-gray-800', [text('台北信義區')])
                ]),
                h3(classes: 'text-lg font-bold mb-2', [text('微風南山 共享空間')]),
                p(classes: 'text-gray-600 mb-4', [text('現代化共享辦公空間，完美適合數位遊牧工作')]),
                div(classes: 'flex items-center space-x-4 text-sm', [
                  div(classes: 'wifi-indicator wifi-excellent', [text('Wi-Fi 優秀')]),
                  div(classes: 'flex items-center', [
                    const Heroicon.solid(HeroiconName.star, classes: 'w-4 h-4 mr-1 text-nomad-interaction'),
                    text('快速充電')
                  ])
                ])
              ]),

              // 測試卡片 3
              div(classes: 'workspace-card-nomad', [
                div(classes: 'flex items-center mb-3', [
                  const Heroicon.solid(HeroiconName.flag, classes: 'w-5 h-5 text-nomad-workspace mr-2'),
                  span(classes: 'font-semibold text-gray-800', [text('台北中山區')])
                ]),
                h3(classes: 'text-lg font-bold mb-2', [text('誠品書店 松菸店')]),
                p(classes: 'text-gray-600 mb-4', [text('書香環繞的工作環境，安靜舒適適合專注工作')]),
                div(classes: 'flex items-center space-x-4 text-sm', [
                  div(classes: 'wifi-indicator wifi-good', [text('Wi-Fi 良好')]),
                  div(classes: 'flex items-center', [
                    const Heroicon.outline(HeroiconName.bookmark, classes: 'w-4 h-4 mr-1'),
                    text('閱讀氛圍')
                  ])
                ])
              ])
            ])
          ])
        ]);
  }
}
